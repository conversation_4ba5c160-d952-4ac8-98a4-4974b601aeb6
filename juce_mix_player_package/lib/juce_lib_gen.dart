// AUTO GENERATED FILE, DO NOT EDIT.
//
// Generated by `package:ffigen`.
// ignore_for_file: type=lint
import 'dart:ffi' as ffi;
import 'package:ffi/ffi.dart' as pkg_ffi;

/// Generate c header to dart bindings
class JuceLibGen {
  /// Holds the symbol lookup function.
  final ffi.Pointer<T> Function<T extends ffi.NativeType>(String symbolName)
      _lookup;

  /// The symbols are looked up in [dynamicLibrary].
  JuceLibGen(ffi.DynamicLibrary dynamicLibrary)
      : _lookup = dynamicLibrary.lookup;

  /// The symbols are looked up with [lookup].
  JuceLibGen.fromLookup(
      ffi.Pointer<T> Function<T extends ffi.NativeType>(String symbolName)
          lookup)
      : _lookup = lookup;

  void juce_init() {
    return _juce_init();
  }

  late final _juce_initPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function()>>('juce_init');
  late final _juce_init = _juce_initPtr.asFunction<void Function()>();

  void Java_com_rmsl_juce_Native_juceMessageManagerInit() {
    return _Java_com_rmsl_juce_Native_juceMessageManagerInit();
  }

  late final _Java_com_rmsl_juce_Native_juceMessageManagerInitPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function()>>(
          'Java_com_rmsl_juce_Native_juceMessageManagerInit');
  late final _Java_com_rmsl_juce_Native_juceMessageManagerInit =
      _Java_com_rmsl_juce_Native_juceMessageManagerInitPtr.asFunction<
          void Function()>();

  void juce_enableLogs(
    int enable,
  ) {
    return _juce_enableLogs(
      enable,
    );
  }

  late final _juce_enableLogsPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Int)>>(
          'juce_enableLogs');
  late final _juce_enableLogs =
      _juce_enableLogsPtr.asFunction<void Function(int)>();

  ffi.Pointer<ffi.Void> JuceMixPlayer_init() {
    return _JuceMixPlayer_init();
  }

  late final _JuceMixPlayer_initPtr =
      _lookup<ffi.NativeFunction<ffi.Pointer<ffi.Void> Function()>>(
          'JuceMixPlayer_init');
  late final _JuceMixPlayer_init =
      _JuceMixPlayer_initPtr.asFunction<ffi.Pointer<ffi.Void> Function()>();

  void JuceMixPlayer_deinit(
    ffi.Pointer<ffi.Void> ptr,
  ) {
    return _JuceMixPlayer_deinit(
      ptr,
    );
  }

  late final _JuceMixPlayer_deinitPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Pointer<ffi.Void>)>>(
          'JuceMixPlayer_deinit');
  late final _JuceMixPlayer_deinit = _JuceMixPlayer_deinitPtr.asFunction<
      void Function(ffi.Pointer<ffi.Void>)>();

  void JuceMixPlayer_play(
    ffi.Pointer<ffi.Void> ptr,
  ) {
    return _JuceMixPlayer_play(
      ptr,
    );
  }

  late final _JuceMixPlayer_playPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Pointer<ffi.Void>)>>(
          'JuceMixPlayer_play');
  late final _JuceMixPlayer_play =
      _JuceMixPlayer_playPtr.asFunction<void Function(ffi.Pointer<ffi.Void>)>();

  void JuceMixPlayer_pause(
    ffi.Pointer<ffi.Void> ptr,
  ) {
    return _JuceMixPlayer_pause(
      ptr,
    );
  }

  late final _JuceMixPlayer_pausePtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Pointer<ffi.Void>)>>(
          'JuceMixPlayer_pause');
  late final _JuceMixPlayer_pause = _JuceMixPlayer_pausePtr.asFunction<
      void Function(ffi.Pointer<ffi.Void>)>();

  void JuceMixPlayer_stop(
    ffi.Pointer<ffi.Void> ptr,
  ) {
    return _JuceMixPlayer_stop(
      ptr,
    );
  }

  late final _JuceMixPlayer_stopPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Pointer<ffi.Void>)>>(
          'JuceMixPlayer_stop');
  late final _JuceMixPlayer_stop =
      _JuceMixPlayer_stopPtr.asFunction<void Function(ffi.Pointer<ffi.Void>)>();

  void JuceMixPlayer_set(
    ffi.Pointer<ffi.Void> ptr,
    ffi.Pointer<pkg_ffi.Utf8> json,
  ) {
    return _JuceMixPlayer_set(
      ptr,
      json,
    );
  }

  late final _JuceMixPlayer_setPtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(ffi.Pointer<ffi.Void>,
              ffi.Pointer<pkg_ffi.Utf8>)>>('JuceMixPlayer_set');
  late final _JuceMixPlayer_set = _JuceMixPlayer_setPtr.asFunction<
      void Function(ffi.Pointer<ffi.Void>, ffi.Pointer<pkg_ffi.Utf8>)>();

  void JuceMixPlayer_setSettings(
    ffi.Pointer<ffi.Void> ptr,
    ffi.Pointer<pkg_ffi.Utf8> json,
  ) {
    return _JuceMixPlayer_setSettings(
      ptr,
      json,
    );
  }

  late final _JuceMixPlayer_setSettingsPtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(ffi.Pointer<ffi.Void>,
              ffi.Pointer<pkg_ffi.Utf8>)>>('JuceMixPlayer_setSettings');
  late final _JuceMixPlayer_setSettings =
      _JuceMixPlayer_setSettingsPtr.asFunction<
          void Function(ffi.Pointer<ffi.Void>, ffi.Pointer<pkg_ffi.Utf8>)>();

  void JuceMixPlayer_onStateUpdate(
    ffi.Pointer<ffi.Void> ptr,
    ffi.Pointer<
            ffi.NativeFunction<
                ffi.Void Function(
                    ffi.Pointer<ffi.Void>, ffi.Pointer<pkg_ffi.Utf8>)>>
        JuceMixPlayerCallbackString,
  ) {
    return _JuceMixPlayer_onStateUpdate(
      ptr,
      JuceMixPlayerCallbackString,
    );
  }

  late final _JuceMixPlayer_onStateUpdatePtr = _lookup<
          ffi.NativeFunction<
              ffi.Void Function(
                  ffi.Pointer<ffi.Void>,
                  ffi.Pointer<
                      ffi.NativeFunction<
                          ffi.Void Function(ffi.Pointer<ffi.Void>,
                              ffi.Pointer<pkg_ffi.Utf8>)>>)>>(
      'JuceMixPlayer_onStateUpdate');
  late final _JuceMixPlayer_onStateUpdate =
      _JuceMixPlayer_onStateUpdatePtr.asFunction<
          void Function(
              ffi.Pointer<ffi.Void>,
              ffi.Pointer<
                  ffi.NativeFunction<
                      ffi.Void Function(ffi.Pointer<ffi.Void>,
                          ffi.Pointer<pkg_ffi.Utf8>)>>)>();

  /// callback with progress value range 0 to 1
  void JuceMixPlayer_onProgress(
    ffi.Pointer<ffi.Void> ptr,
    ffi.Pointer<
            ffi.NativeFunction<
                ffi.Void Function(ffi.Pointer<ffi.Void>, ffi.Float)>>
        onProgress,
  ) {
    return _JuceMixPlayer_onProgress(
      ptr,
      onProgress,
    );
  }

  late final _JuceMixPlayer_onProgressPtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<ffi.Void>,
              ffi.Pointer<
                  ffi.NativeFunction<
                      ffi.Void Function(ffi.Pointer<ffi.Void>,
                          ffi.Float)>>)>>('JuceMixPlayer_onProgress');
  late final _JuceMixPlayer_onProgress =
      _JuceMixPlayer_onProgressPtr.asFunction<
          void Function(
              ffi.Pointer<ffi.Void>,
              ffi.Pointer<
                  ffi.NativeFunction<
                      ffi.Void Function(ffi.Pointer<ffi.Void>, ffi.Float)>>)>();

  void JuceMixPlayer_onError(
    ffi.Pointer<ffi.Void> ptr,
    ffi.Pointer<
            ffi.NativeFunction<
                ffi.Void Function(
                    ffi.Pointer<ffi.Void>, ffi.Pointer<pkg_ffi.Utf8>)>>
        onError,
  ) {
    return _JuceMixPlayer_onError(
      ptr,
      onError,
    );
  }

  late final _JuceMixPlayer_onErrorPtr = _lookup<
          ffi.NativeFunction<
              ffi.Void Function(
                  ffi.Pointer<ffi.Void>,
                  ffi.Pointer<
                      ffi.NativeFunction<
                          ffi.Void Function(ffi.Pointer<ffi.Void>,
                              ffi.Pointer<pkg_ffi.Utf8>)>>)>>(
      'JuceMixPlayer_onError');
  late final _JuceMixPlayer_onError = _JuceMixPlayer_onErrorPtr.asFunction<
      void Function(
          ffi.Pointer<ffi.Void>,
          ffi.Pointer<
              ffi.NativeFunction<
                  ffi.Void Function(
                      ffi.Pointer<ffi.Void>, ffi.Pointer<pkg_ffi.Utf8>)>>)>();

  /// value returns time in seconds
  double JuceMixPlayer_getDuration(
    ffi.Pointer<ffi.Void> ptr,
  ) {
    return _JuceMixPlayer_getDuration(
      ptr,
    );
  }

  late final _JuceMixPlayer_getDurationPtr =
      _lookup<ffi.NativeFunction<ffi.Float Function(ffi.Pointer<ffi.Void>)>>(
          'JuceMixPlayer_getDuration');
  late final _JuceMixPlayer_getDuration = _JuceMixPlayer_getDurationPtr
      .asFunction<double Function(ffi.Pointer<ffi.Void>)>();

  /// returns 1 if playing else 0
  int JuceMixPlayer_isPlaying(
    ffi.Pointer<ffi.Void> ptr,
  ) {
    return _JuceMixPlayer_isPlaying(
      ptr,
    );
  }

  late final _JuceMixPlayer_isPlayingPtr =
      _lookup<ffi.NativeFunction<ffi.Int Function(ffi.Pointer<ffi.Void>)>>(
          'JuceMixPlayer_isPlaying');
  late final _JuceMixPlayer_isPlaying = _JuceMixPlayer_isPlayingPtr.asFunction<
      int Function(ffi.Pointer<ffi.Void>)>();

  /// value range 0 to 1
  void JuceMixPlayer_seek(
    ffi.Pointer<ffi.Void> ptr,
    double value,
  ) {
    return _JuceMixPlayer_seek(
      ptr,
      value,
    );
  }

  late final _JuceMixPlayer_seekPtr = _lookup<
          ffi
          .NativeFunction<ffi.Void Function(ffi.Pointer<ffi.Void>, ffi.Float)>>(
      'JuceMixPlayer_seek');
  late final _JuceMixPlayer_seek = _JuceMixPlayer_seekPtr.asFunction<
      void Function(ffi.Pointer<ffi.Void>, double)>();

  void JuceMixPlayer_prepareRecorder(
    ffi.Pointer<ffi.Void> ptr,
    ffi.Pointer<pkg_ffi.Utf8> file,
  ) {
    return _JuceMixPlayer_prepareRecorder(
      ptr,
      file,
    );
  }

  late final _JuceMixPlayer_prepareRecorderPtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(ffi.Pointer<ffi.Void>,
              ffi.Pointer<pkg_ffi.Utf8>)>>('JuceMixPlayer_prepareRecorder');
  late final _JuceMixPlayer_prepareRecorder =
      _JuceMixPlayer_prepareRecorderPtr.asFunction<
          void Function(ffi.Pointer<ffi.Void>, ffi.Pointer<pkg_ffi.Utf8>)>();

  void JuceMixPlayer_startRecorder(
    ffi.Pointer<ffi.Void> ptr,
  ) {
    return _JuceMixPlayer_startRecorder(
      ptr,
    );
  }

  late final _JuceMixPlayer_startRecorderPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Pointer<ffi.Void>)>>(
          'JuceMixPlayer_startRecorder');
  late final _JuceMixPlayer_startRecorder = _JuceMixPlayer_startRecorderPtr
      .asFunction<void Function(ffi.Pointer<ffi.Void>)>();

  void JuceMixPlayer_stopRecorder(
    ffi.Pointer<ffi.Void> ptr,
  ) {
    return _JuceMixPlayer_stopRecorder(
      ptr,
    );
  }

  late final _JuceMixPlayer_stopRecorderPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Pointer<ffi.Void>)>>(
          'JuceMixPlayer_stopRecorder');
  late final _JuceMixPlayer_stopRecorder = _JuceMixPlayer_stopRecorderPtr
      .asFunction<void Function(ffi.Pointer<ffi.Void>)>();

  void JuceMixPlayer_onRecStateUpdate(
    ffi.Pointer<ffi.Void> ptr,
    ffi.Pointer<
            ffi.NativeFunction<
                ffi.Void Function(
                    ffi.Pointer<ffi.Void>, ffi.Pointer<pkg_ffi.Utf8>)>>
        onStateUpdate,
  ) {
    return _JuceMixPlayer_onRecStateUpdate(
      ptr,
      onStateUpdate,
    );
  }

  late final _JuceMixPlayer_onRecStateUpdatePtr = _lookup<
          ffi.NativeFunction<
              ffi.Void Function(
                  ffi.Pointer<ffi.Void>,
                  ffi.Pointer<
                      ffi.NativeFunction<
                          ffi.Void Function(ffi.Pointer<ffi.Void>,
                              ffi.Pointer<pkg_ffi.Utf8>)>>)>>(
      'JuceMixPlayer_onRecStateUpdate');
  late final _JuceMixPlayer_onRecStateUpdate =
      _JuceMixPlayer_onRecStateUpdatePtr.asFunction<
          void Function(
              ffi.Pointer<ffi.Void>,
              ffi.Pointer<
                  ffi.NativeFunction<
                      ffi.Void Function(ffi.Pointer<ffi.Void>,
                          ffi.Pointer<pkg_ffi.Utf8>)>>)>();

  void JuceMixPlayer_onRecProgress(
    ffi.Pointer<ffi.Void> ptr,
    ffi.Pointer<
            ffi.NativeFunction<
                ffi.Void Function(ffi.Pointer<ffi.Void>, ffi.Float)>>
        onProgress,
  ) {
    return _JuceMixPlayer_onRecProgress(
      ptr,
      onProgress,
    );
  }

  late final _JuceMixPlayer_onRecProgressPtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<ffi.Void>,
              ffi.Pointer<
                  ffi.NativeFunction<
                      ffi.Void Function(ffi.Pointer<ffi.Void>,
                          ffi.Float)>>)>>('JuceMixPlayer_onRecProgress');
  late final _JuceMixPlayer_onRecProgress =
      _JuceMixPlayer_onRecProgressPtr.asFunction<
          void Function(
              ffi.Pointer<ffi.Void>,
              ffi.Pointer<
                  ffi.NativeFunction<
                      ffi.Void Function(ffi.Pointer<ffi.Void>, ffi.Float)>>)>();

  void JuceMixPlayer_onRecError(
    ffi.Pointer<ffi.Void> ptr,
    ffi.Pointer<
            ffi.NativeFunction<
                ffi.Void Function(
                    ffi.Pointer<ffi.Void>, ffi.Pointer<pkg_ffi.Utf8>)>>
        onError,
  ) {
    return _JuceMixPlayer_onRecError(
      ptr,
      onError,
    );
  }

  late final _JuceMixPlayer_onRecErrorPtr = _lookup<
          ffi.NativeFunction<
              ffi.Void Function(
                  ffi.Pointer<ffi.Void>,
                  ffi.Pointer<
                      ffi.NativeFunction<
                          ffi.Void Function(ffi.Pointer<ffi.Void>,
                              ffi.Pointer<pkg_ffi.Utf8>)>>)>>(
      'JuceMixPlayer_onRecError');
  late final _JuceMixPlayer_onRecError =
      _JuceMixPlayer_onRecErrorPtr.asFunction<
          void Function(
              ffi.Pointer<ffi.Void>,
              ffi.Pointer<
                  ffi.NativeFunction<
                      ffi.Void Function(ffi.Pointer<ffi.Void>,
                          ffi.Pointer<pkg_ffi.Utf8>)>>)>();

  void JuceMixPlayer_onRecLevel(
    ffi.Pointer<ffi.Void> ptr,
    ffi.Pointer<
            ffi.NativeFunction<
                ffi.Void Function(ffi.Pointer<ffi.Void>, ffi.Float)>>
        onLevel,
  ) {
    return _JuceMixPlayer_onRecLevel(
      ptr,
      onLevel,
    );
  }

  late final _JuceMixPlayer_onRecLevelPtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<ffi.Void>,
              ffi.Pointer<
                  ffi.NativeFunction<
                      ffi.Void Function(ffi.Pointer<ffi.Void>,
                          ffi.Float)>>)>>('JuceMixPlayer_onRecLevel');
  late final _JuceMixPlayer_onRecLevel =
      _JuceMixPlayer_onRecLevelPtr.asFunction<
          void Function(
              ffi.Pointer<ffi.Void>,
              ffi.Pointer<
                  ffi.NativeFunction<
                      ffi.Void Function(ffi.Pointer<ffi.Void>, ffi.Float)>>)>();

  void JuceMixPlayer_onDeviceUpdate(
    ffi.Pointer<ffi.Void> ptr,
    ffi.Pointer<
            ffi.NativeFunction<
                ffi.Void Function(
                    ffi.Pointer<ffi.Void>, ffi.Pointer<pkg_ffi.Utf8>)>>
        onDeviceUpdate,
  ) {
    return _JuceMixPlayer_onDeviceUpdate(
      ptr,
      onDeviceUpdate,
    );
  }

  late final _JuceMixPlayer_onDeviceUpdatePtr = _lookup<
          ffi.NativeFunction<
              ffi.Void Function(
                  ffi.Pointer<ffi.Void>,
                  ffi.Pointer<
                      ffi.NativeFunction<
                          ffi.Void Function(ffi.Pointer<ffi.Void>,
                              ffi.Pointer<pkg_ffi.Utf8>)>>)>>(
      'JuceMixPlayer_onDeviceUpdate');
  late final _JuceMixPlayer_onDeviceUpdate =
      _JuceMixPlayer_onDeviceUpdatePtr.asFunction<
          void Function(
              ffi.Pointer<ffi.Void>,
              ffi.Pointer<
                  ffi.NativeFunction<
                      ffi.Void Function(ffi.Pointer<ffi.Void>,
                          ffi.Pointer<pkg_ffi.Utf8>)>>)>();

  void JuceMixPlayer_setUpdatedDevices(
    ffi.Pointer<ffi.Void> ptr,
    ffi.Pointer<pkg_ffi.Utf8> json,
  ) {
    return _JuceMixPlayer_setUpdatedDevices(
      ptr,
      json,
    );
  }

  late final _JuceMixPlayer_setUpdatedDevicesPtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(ffi.Pointer<ffi.Void>,
              ffi.Pointer<pkg_ffi.Utf8>)>>('JuceMixPlayer_setUpdatedDevices');
  late final _JuceMixPlayer_setUpdatedDevices =
      _JuceMixPlayer_setUpdatedDevicesPtr.asFunction<
          void Function(ffi.Pointer<ffi.Void>, ffi.Pointer<pkg_ffi.Utf8>)>();

  ffi.Pointer<pkg_ffi.Utf8> JuceMixPlayer_getDeviceLatencyInfo(
    ffi.Pointer<ffi.Void> ptr,
  ) {
    return _JuceMixPlayer_getDeviceLatencyInfo(
      ptr,
    );
  }

  late final _JuceMixPlayer_getDeviceLatencyInfoPtr = _lookup<
      ffi.NativeFunction<
          ffi.Pointer<pkg_ffi.Utf8> Function(
              ffi.Pointer<ffi.Void>)>>('JuceMixPlayer_getDeviceLatencyInfo');
  late final _JuceMixPlayer_getDeviceLatencyInfo =
      _JuceMixPlayer_getDeviceLatencyInfoPtr.asFunction<
          ffi.Pointer<pkg_ffi.Utf8> Function(ffi.Pointer<ffi.Void>)>();

  void JuceMixPlayer_export(
    ffi.Pointer<ffi.Void> ptr,
    ffi.Pointer<pkg_ffi.Utf8> outputPath,
    ffi.Pointer<
            ffi.NativeFunction<ffi.Void Function(ffi.Pointer<pkg_ffi.Utf8>)>>
        completion,
  ) {
    return _JuceMixPlayer_export(
      ptr,
      outputPath,
      completion,
    );
  }

  late final _JuceMixPlayer_exportPtr = _lookup<
          ffi.NativeFunction<
              ffi.Void Function(
                  ffi.Pointer<ffi.Void>,
                  ffi.Pointer<pkg_ffi.Utf8>,
                  ffi.Pointer<
                      ffi.NativeFunction<
                          ffi.Void Function(ffi.Pointer<pkg_ffi.Utf8>)>>)>>(
      'JuceMixPlayer_export');
  late final _JuceMixPlayer_export = _JuceMixPlayer_exportPtr.asFunction<
      void Function(
          ffi.Pointer<ffi.Void>,
          ffi.Pointer<pkg_ffi.Utf8>,
          ffi.Pointer<
              ffi.NativeFunction<
                  ffi.Void Function(ffi.Pointer<pkg_ffi.Utf8>)>>)>();

  int JuceMixPlayer_fileExists(
    ffi.Pointer<pkg_ffi.Utf8> filePath,
  ) {
    return _JuceMixPlayer_fileExists(
      filePath,
    );
  }

  late final _JuceMixPlayer_fileExistsPtr =
      _lookup<ffi.NativeFunction<ffi.Int Function(ffi.Pointer<pkg_ffi.Utf8>)>>(
          'JuceMixPlayer_fileExists');
  late final _JuceMixPlayer_fileExists = _JuceMixPlayer_fileExistsPtr
      .asFunction<int Function(ffi.Pointer<pkg_ffi.Utf8>)>();

  ffi.Pointer<ffi.Void> GstPlayer_init() {
    return _GstPlayer_init();
  }

  late final _GstPlayer_initPtr =
      _lookup<ffi.NativeFunction<ffi.Pointer<ffi.Void> Function()>>(
          'GstPlayer_init');
  late final _GstPlayer_init =
      _GstPlayer_initPtr.asFunction<ffi.Pointer<ffi.Void> Function()>();

  void GstPlayer_deinit(
    ffi.Pointer<ffi.Void> ptr,
  ) {
    return _GstPlayer_deinit(
      ptr,
    );
  }

  late final _GstPlayer_deinitPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Pointer<ffi.Void>)>>(
          'GstPlayer_deinit');
  late final _GstPlayer_deinit =
      _GstPlayer_deinitPtr.asFunction<void Function(ffi.Pointer<ffi.Void>)>();

  void GstPlayer_setVideoPath(
    ffi.Pointer<ffi.Void> ptr,
    ffi.Pointer<pkg_ffi.Utf8> path,
  ) {
    return _GstPlayer_setVideoPath(
      ptr,
      path,
    );
  }

  late final _GstPlayer_setVideoPathPtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(ffi.Pointer<ffi.Void>,
              ffi.Pointer<pkg_ffi.Utf8>)>>('GstPlayer_setVideoPath');
  late final _GstPlayer_setVideoPath = _GstPlayer_setVideoPathPtr.asFunction<
      void Function(ffi.Pointer<ffi.Void>, ffi.Pointer<pkg_ffi.Utf8>)>();

  void GstPlayer_play(
    ffi.Pointer<ffi.Void> ptr,
  ) {
    return _GstPlayer_play(
      ptr,
    );
  }

  late final _GstPlayer_playPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Pointer<ffi.Void>)>>(
          'GstPlayer_play');
  late final _GstPlayer_play =
      _GstPlayer_playPtr.asFunction<void Function(ffi.Pointer<ffi.Void>)>();

  void GstPlayer_pause(
    ffi.Pointer<ffi.Void> ptr,
  ) {
    return _GstPlayer_pause(
      ptr,
    );
  }

  late final _GstPlayer_pausePtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Pointer<ffi.Void>)>>(
          'GstPlayer_pause');
  late final _GstPlayer_pause =
      _GstPlayer_pausePtr.asFunction<void Function(ffi.Pointer<ffi.Void>)>();

  void GstPlayer_stop(
    ffi.Pointer<ffi.Void> ptr,
  ) {
    return _GstPlayer_stop(
      ptr,
    );
  }

  late final _GstPlayer_stopPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Pointer<ffi.Void>)>>(
          'GstPlayer_stop');
  late final _GstPlayer_stop =
      _GstPlayer_stopPtr.asFunction<void Function(ffi.Pointer<ffi.Void>)>();

  void GstPlayer_seek(
    ffi.Pointer<ffi.Void> ptr,
    double normalized,
  ) {
    return _GstPlayer_seek(
      ptr,
      normalized,
    );
  }

  late final _GstPlayer_seekPtr = _lookup<
          ffi
          .NativeFunction<ffi.Void Function(ffi.Pointer<ffi.Void>, ffi.Float)>>(
      'GstPlayer_seek');
  late final _GstPlayer_seek = _GstPlayer_seekPtr.asFunction<
      void Function(ffi.Pointer<ffi.Void>, double)>();

  int GstPlayer_isPlaying(
    ffi.Pointer<ffi.Void> ptr,
  ) {
    return _GstPlayer_isPlaying(
      ptr,
    );
  }

  late final _GstPlayer_isPlayingPtr =
      _lookup<ffi.NativeFunction<ffi.Int Function(ffi.Pointer<ffi.Void>)>>(
          'GstPlayer_isPlaying');
  late final _GstPlayer_isPlaying =
      _GstPlayer_isPlayingPtr.asFunction<int Function(ffi.Pointer<ffi.Void>)>();

  double GstPlayer_getDuration(
    ffi.Pointer<ffi.Void> ptr,
  ) {
    return _GstPlayer_getDuration(
      ptr,
    );
  }

  late final _GstPlayer_getDurationPtr =
      _lookup<ffi.NativeFunction<ffi.Float Function(ffi.Pointer<ffi.Void>)>>(
          'GstPlayer_getDuration');
  late final _GstPlayer_getDuration = _GstPlayer_getDurationPtr.asFunction<
      double Function(ffi.Pointer<ffi.Void>)>();

  void GstPlayer_onStateUpdate(
    ffi.Pointer<ffi.Void> ptr,
    ffi.Pointer<
            ffi.NativeFunction<
                ffi.Void Function(
                    ffi.Pointer<ffi.Void>, ffi.Pointer<pkg_ffi.Utf8>)>>
        callback,
  ) {
    return _GstPlayer_onStateUpdate(
      ptr,
      callback,
    );
  }

  late final _GstPlayer_onStateUpdatePtr = _lookup<
          ffi.NativeFunction<
              ffi.Void Function(
                  ffi.Pointer<ffi.Void>,
                  ffi.Pointer<
                      ffi.NativeFunction<
                          ffi.Void Function(ffi.Pointer<ffi.Void>,
                              ffi.Pointer<pkg_ffi.Utf8>)>>)>>(
      'GstPlayer_onStateUpdate');
  late final _GstPlayer_onStateUpdate = _GstPlayer_onStateUpdatePtr.asFunction<
      void Function(
          ffi.Pointer<ffi.Void>,
          ffi.Pointer<
              ffi.NativeFunction<
                  ffi.Void Function(
                      ffi.Pointer<ffi.Void>, ffi.Pointer<pkg_ffi.Utf8>)>>)>();

  void GstPlayer_onProgress(
    ffi.Pointer<ffi.Void> ptr,
    ffi.Pointer<
            ffi.NativeFunction<
                ffi.Void Function(ffi.Pointer<ffi.Void>, ffi.Float)>>
        callback,
  ) {
    return _GstPlayer_onProgress(
      ptr,
      callback,
    );
  }

  late final _GstPlayer_onProgressPtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<ffi.Void>,
              ffi.Pointer<
                  ffi.NativeFunction<
                      ffi.Void Function(ffi.Pointer<ffi.Void>,
                          ffi.Float)>>)>>('GstPlayer_onProgress');
  late final _GstPlayer_onProgress = _GstPlayer_onProgressPtr.asFunction<
      void Function(
          ffi.Pointer<ffi.Void>,
          ffi.Pointer<
              ffi.NativeFunction<
                  ffi.Void Function(ffi.Pointer<ffi.Void>, ffi.Float)>>)>();

  void GstPlayer_onError(
    ffi.Pointer<ffi.Void> ptr,
    ffi.Pointer<
            ffi.NativeFunction<
                ffi.Void Function(
                    ffi.Pointer<ffi.Void>, ffi.Pointer<pkg_ffi.Utf8>)>>
        callback,
  ) {
    return _GstPlayer_onError(
      ptr,
      callback,
    );
  }

  late final _GstPlayer_onErrorPtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<ffi.Void>,
              ffi.Pointer<
                  ffi.NativeFunction<
                      ffi.Void Function(ffi.Pointer<ffi.Void>,
                          ffi.Pointer<pkg_ffi.Utf8>)>>)>>('GstPlayer_onError');
  late final _GstPlayer_onError = _GstPlayer_onErrorPtr.asFunction<
      void Function(
          ffi.Pointer<ffi.Void>,
          ffi.Pointer<
              ffi.NativeFunction<
                  ffi.Void Function(
                      ffi.Pointer<ffi.Void>, ffi.Pointer<pkg_ffi.Utf8>)>>)>();

  void GstPlayer_setSurfaceHandle(
    ffi.Pointer<ffi.Void> ptr,
    ffi.Pointer<ffi.Void> nativeSurface,
  ) {
    return _GstPlayer_setSurfaceHandle(
      ptr,
      nativeSurface,
    );
  }

  late final _GstPlayer_setSurfaceHandlePtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(ffi.Pointer<ffi.Void>,
              ffi.Pointer<ffi.Void>)>>('GstPlayer_setSurfaceHandle');
  late final _GstPlayer_setSurfaceHandle =
      _GstPlayer_setSurfaceHandlePtr.asFunction<
          void Function(ffi.Pointer<ffi.Void>, ffi.Pointer<ffi.Void>)>();

  void GstPlayer_setMuteEmbeddedAudio(
    ffi.Pointer<ffi.Void> ptr,
    int mute,
  ) {
    return _GstPlayer_setMuteEmbeddedAudio(
      ptr,
      mute,
    );
  }

  late final _GstPlayer_setMuteEmbeddedAudioPtr = _lookup<
          ffi
          .NativeFunction<ffi.Void Function(ffi.Pointer<ffi.Void>, ffi.Int)>>(
      'GstPlayer_setMuteEmbeddedAudio');
  late final _GstPlayer_setMuteEmbeddedAudio =
      _GstPlayer_setMuteEmbeddedAudioPtr.asFunction<
          void Function(ffi.Pointer<ffi.Void>, int)>();
}
