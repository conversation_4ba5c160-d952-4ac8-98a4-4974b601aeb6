pluginManagement {

    def flutterSdkPath = {
        def properties = new Properties()
        file("local.properties").withInputStream { properties.load(it) }
        def flutterSdkPath = properties.getProperty("flutter.sdk")
        assert flutterSdkPath != null, "flutter.sdk not set in local.properties"
        return flutterSdkPath
    }()

    include(':juce_jni')
    project(':juce_jni').projectDir = new File(rootProject.projectDir, '../../juce_lib/Builds/Android/lib')

    includeBuild("$flutterSdkPath/packages/flutter_tools/gradle")

    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

plugins {
    id "dev.flutter.flutter-plugin-loader" version "1.0.0"
    id "com.android.application" version "8.11.1" apply false  // Updated from 7.3.0 to 8.1.0
    id "org.jetbrains.kotlin.android" version "2.1.0" apply false
}

include ":app"
